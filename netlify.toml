[build]
  # Diretório base para o build
  base = "frontend"
  
  # Comando para fazer o build
  command = "npm run build"
  
  # Diretório onde ficam os arquivos buildados
  publish = "dist"

[build.environment]
  # Versão do Node.js
  NODE_VERSION = "18"

# Configurações para SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers de segurança
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

# Cache para assets estáticos
[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"
