{"name": "biblioteca-online", "version": "1.0.0", "description": "Sistema de gerenciamento de biblioteca com Node.js e React", "scripts": {"start": "node backend/index.js", "dev": "nodemon backend/index.js", "frontend": "cd frontend && npm run dev", "backend": "cd backend && npm start", "install-all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "devDependencies": {"sass": "^1.83.4"}}