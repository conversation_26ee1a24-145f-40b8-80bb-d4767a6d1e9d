{"name": "front", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint src --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"axios": "^1.3.6", "localforage": "^1.10.0", "match-sorter": "^6.3.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.10.0", "sort-by": "^1.2.0"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@vitejs/plugin-react": "^4.0.0-beta.0", "eslint": "^8.38.0", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "sass": "^1.62.0", "vite": "^4.3.0"}}