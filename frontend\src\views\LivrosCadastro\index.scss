.livrosCadastro{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px 10%;
    font-family: Arial, Helvetica, sans-serif;
    min-height: 70vh;

    h1{
        margin: 30px 0;
        color: #333;
        text-align: center;
    }

    .form-container {
        background: white;
        padding: 30px;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        width: 100%;
        max-width: 500px;
    }

    .form-group{
        display: flex;
        flex-direction: column;
        margin-bottom: 20px;

        label{
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
            font-size: 14px;
        }

        input{
            padding: 12px;
            height: 45px;
            font-size: 14px;
            border-radius: 5px;
            border: 2px solid #ddd;
            transition: border-color 0.3s ease;

            &:focus {
                outline: none;
                border-color: var(--primary-color);
            }

            &.error {
                border-color: #dc3545;
            }

            &.disabled {
                background-color: #f8f9fa;
                color: #6c757d;
                cursor: not-allowed;
            }

            &::placeholder {
                color: #999;
            }
        }

        .error-message {
            color: #dc3545;
            font-size: 12px;
            margin-top: 5px;
        }

        small {
            color: #6c757d;
            font-size: 12px;
            margin-top: 5px;
        }
    }

    .form-actions {
        display: flex;
        gap: 15px;
        margin-top: 30px;

        button {
            flex: 1;
            height: 45px;
            font-size: 14px;
            font-weight: bold;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
        }

        .btn-primary {
            background-color: var(--primary-color);
            color: white;

            &:hover:not(:disabled) {
                background-color: var(--primary-color-hover);
            }
        }

        .btn-secondary {
            background-color: var(--secondary-color);
            color: white;

            &:hover:not(:disabled) {
                background-color: var(--secondary-color-hover);
            }
        }
    }

    .loading {
        text-align: center;
        padding: 40px;
        color: #666;
    }
}