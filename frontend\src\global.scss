/* Global SCSS Styles */

/* Reset básico */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Variáveis globais */
:root {
  --primary-color: #007bff;
  --primary-color-hover: #0056b3;
  --secondary-color: #6c757d;
  --secondary-color-hover: #545b62;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;

  --font-family-primary: 'Arial', sans-serif;
  --font-size-base: 16px;
  --line-height-base: 1.5;

  --border-radius: 4px;
  --box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Estilos base do body */
body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--dark-color);
  background-color: var(--light-color);
}

/* Tipografia */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: 0.5rem;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: 1rem;
}

/* Links */
a {
  color: var(--primary-color);
  text-decoration: none;
  transition: var(--transition);

  &:hover {
    color: var(--primary-color-hover);
    text-decoration: underline;
  }
}

/* Botões */
.btn {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  margin-bottom: 0;
  font-size: var(--font-size-base);
  font-weight: 400;
  line-height: var(--line-height-base);
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  cursor: pointer;
  border: 1px solid transparent;
  border-radius: var(--border-radius);
  transition: var(--transition);
  
  &:hover {
    opacity: 0.8;
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

.btn-primary {
  color: white;
  background-color: var(--primary-color);
  border-color: var(--primary-color);

  &:hover {
    background-color: var(--primary-color-hover);
    border-color: var(--primary-color-hover);
  }
}

.btn-secondary {
  color: white;
  background-color: var(--secondary-color);
  border-color: var(--secondary-color);

  &:hover {
    background-color: var(--secondary-color-hover);
    border-color: var(--secondary-color-hover);
  }
}

/* Formulários */
.form-control {
  display: block;
  width: 100%;
  padding: 0.375rem 0.75rem;
  font-size: var(--font-size-base);
  line-height: var(--line-height-base);
  color: var(--dark-color);
  background-color: white;
  border: 1px solid #ced4da;
  border-radius: var(--border-radius);
  transition: var(--transition);
  
  &:focus {
    outline: 0;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
}

/* Utilitários */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }
.mt-5 { margin-top: 3rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }
.mb-5 { margin-bottom: 3rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }
.p-5 { padding: 3rem; }

/* Container */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Responsividade */
@media (max-width: 768px) {
  .container {
    padding: 0 10px;
  }
  
  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }
  h3 { font-size: 1.5rem; }
}
