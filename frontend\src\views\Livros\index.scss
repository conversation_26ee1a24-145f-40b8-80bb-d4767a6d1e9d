.livros{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    padding:0px 10%;
    font-family: Arial, Helvetica, sans-serif;

    h1{
        margin:30px;
    }

    ul{
        width: 100%;
        list-style: none;
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;

        li{
            margin:10px;
            border: 1px solid rgb(255, 255, 255);
            border-radius: 5px;
            background-color: white;
            box-shadow: 2px 2px 5px rgb(66, 66, 66);
            padding:15px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width:350px;
            text-decoration: none;
            font-weight: bold;
            font-size:12pt;
            color:black;

            .livro-info {
                width: 100%;
                margin-bottom: 10px;

                h3 {
                    margin: 0 0 8px 0;
                    color: #333;
                    font-size: 16pt;
                }

                p {
                    margin: 4px 0;
                    font-size: 10pt;
                    font-weight: normal;
                    color: #666;

                    strong {
                        color: #333;
                        font-weight: bold;
                    }
                }
            }
        }
        span{   
            font-size:8pt;
            font-weight: lighter;
        }

        .botoes{
            display: flex;
            width: 100%;

            justify-content: flex-end;
            align-items: bottom;
            
        }

        .btn{
            width:30px;
            height:30px;
            background-color: white;
            border: 0px solid rgb(179, 179, 179); 
            border-radius: 5px;
            display:flex;
            justify-content: center;
            align-items: center;
            margin:3px;            
        }
                
        .btn:hover{                        
            opacity:0.8;
            border: 0px solid rgb(238, 238, 238); 
            box-shadow: 0px 0px 2px rgb(66, 66, 66); 
            color:#303030;        
        }

        .edit{
            width:30px;
            background-color:blue;
            color:white;
        }
        
        .delete{
            width:30px;
            background-color:red;
            color:white;
        }
    }

    // Novos estilos para funcionalidades aprimoradas
    .header-section {
        width: 100%;
        margin-bottom: 30px;
        text-align: center;

        h1 {
            margin-bottom: 20px;
        }
    }

    .search-section {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
        margin-bottom: 20px;

        .search-input {
            padding: 10px 15px;
            font-size: 14px;
            border: 2px solid #ddd;
            border-radius: 25px;
            width: 300px;
            outline: none;
            transition: border-color 0.3s ease;

            &:focus {
                border-color: var(--primary-color);
            }
        }

        .btn-refresh {
            padding: 10px 20px;
            background-color: var(--secondary-color);
            color: white;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s ease;

            &:hover {
                background-color: var(--secondary-color-hover);
            }
        }
    }

    .results-info {
        text-align: center;
        margin-bottom: 20px;
        color: #666;
        font-size: 14px;
    }

    .no-books {
        text-align: center;
        padding: 60px 20px;
        color: #666;

        h3 {
            margin-bottom: 15px;
            color: #333;
        }

        p {
            margin-bottom: 25px;
            font-size: 16px;
        }

        .btn-primary {
            display: inline-block;
            padding: 12px 25px;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: background-color 0.3s ease;

            &:hover {
                background-color: var(--primary-color-hover);
            }
        }
    }

    .books-grid {
        list-style: none;
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 20px;
        padding: 0;
    }

    .book-card {
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        padding: 20px;
        transition: transform 0.3s ease, box-shadow 0.3s ease;

        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
        }
    }

    .loading {
        text-align: center;
        padding: 60px 20px;
        color: #666;

        h1 {
            margin-bottom: 20px;
        }

        p {
            font-size: 16px;
        }
    }
}