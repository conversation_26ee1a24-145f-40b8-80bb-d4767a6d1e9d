.livros{
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: flex-start;
    padding:0px 10%;
    font-family: Arial, Helvetica, sans-serif;

    h1{
        margin:30px;
    }

    ul{
        width: 100%;
        list-style: none;
        display: flex;
        justify-content: space-around;
        flex-wrap: wrap;

        li{
            margin:10px;
            border: 1px solid rgb(255, 255, 255);
            border-radius: 5px;
            background-color: white;
            box-shadow: 2px 2px 5px rgb(66, 66, 66);
            padding:15px;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            width:350px;
            text-decoration: none;
            font-weight: bold;
            font-size:12pt;
            color:black;

            .livro-info {
                width: 100%;
                margin-bottom: 10px;

                h3 {
                    margin: 0 0 8px 0;
                    color: #333;
                    font-size: 16pt;
                }

                p {
                    margin: 4px 0;
                    font-size: 10pt;
                    font-weight: normal;
                    color: #666;

                    strong {
                        color: #333;
                        font-weight: bold;
                    }
                }
            }
        }
        span{   
            font-size:8pt;
            font-weight: lighter;
        }

        .botoes{
            display: flex;
            width: 100%;

            justify-content: flex-end;
            align-items: bottom;
            
        }

        .btn{
            width:30px;
            height:30px;
            background-color: white;
            border: 0px solid rgb(179, 179, 179); 
            border-radius: 5px;
            display:flex;
            justify-content: center;
            align-items: center;
            margin:3px;            
        }
                
        .btn:hover{                        
            opacity:0.8;
            border: 0px solid rgb(238, 238, 238); 
            box-shadow: 0px 0px 2px rgb(66, 66, 66); 
            color:#303030;        
        }

        .edit{
            width:30px;
            background-color:blue;
            color:white;
        }
        
        .delete{
            width:30px;
            background-color:red;
            color:white;
        }


    }
}